{"name": "drawdb-angular-host", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:drawdb-angular-host": "node dist/drawdb-angular-host/server/server.mjs", "run:all": "node node_modules/@angular-architects/module-federation/src/server/mf-dev-server.js"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular-architects/module-federation": "^20.0.0", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/platform-server": "^20.0.0", "@angular/router": "^20.0.0", "@angular/ssr": "^20.0.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "express": "^5.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.5", "@angular/build": "^20.0.5", "@angular/cli": "^20.0.5", "@angular/compiler-cli": "^20.0.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ngx-build-plus": "^20.0.0", "typescript": "~5.8.2"}}