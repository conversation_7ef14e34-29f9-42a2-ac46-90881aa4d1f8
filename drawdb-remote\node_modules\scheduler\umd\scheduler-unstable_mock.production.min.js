/**
 * @license React
 * scheduler-unstable_mock.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(a,p){"object"===typeof exports&&"undefined"!==typeof module?p(exports):"function"===typeof define&&define.amd?define(["exports"],p):(a=a||self,p(a.SchedulerMock={}))})(this,function(a){function p(b,k){var c=b.length;b.push(k);a:for(;0<c;){var a=c-1>>>1,v=b[a];if(0<z(v,k))b[a]=k,b[c]=v,c=a;else break a}}function m(b){return 0===b.length?null:b[0]}function A(b){if(0===b.length)return null;var k=b[0],c=b.pop();if(c!==k){b[0]=c;a:for(var a=0,v=b.length,f=v>>>1;a<f;){var d=2*(a+
1)-1,g=b[d],e=d+1,h=b[e];if(0>z(g,c))e<v&&0>z(h,g)?(b[a]=h,b[e]=c,a=e):(b[a]=g,b[d]=c,a=d);else if(e<v&&0>z(h,c))b[a]=h,b[e]=c,a=e;else break a}}return k}function z(b,a){var k=b.sortIndex-a.sortIndex;return 0!==k?k:b.id-a.id}function D(b){for(var a=m(r);null!==a;){if(null===a.callback)A(r);else if(a.startTime<=b)A(r),a.sortIndex=a.expirationTime,p(n,a);else break;a=m(r)}}function E(b){y=!1;D(b);if(!u)if(null!==m(n))u=!0,f=F;else{var a=m(r);null!==a&&(b=a.startTime-b,q=E,t=g+b)}}function F(b,a){u=
!1;y&&(y=!1,q=null,t=-1);B=!0;var c=d;try{D(a);for(h=m(n);null!==h&&(!(h.expirationTime>a)||b&&!I());){var k=h.callback;if("function"===typeof k){h.callback=null;d=h.priorityLevel;var e=k(h.expirationTime<=a);a=g;"function"===typeof e?h.callback=e:h===m(n)&&A(n);D(a)}else A(n);h=m(n)}if(null!==h)var f=!0;else{var l=m(r);if(null!==l){var p=l.startTime-a;q=E;t=g+p}f=!1}return f}finally{h=null,d=c,B=!1}}function I(){return 0===w&&null===l||-1!==w&&null!==l&&l.length>=w||G&&C?x=!0:!1}function J(){if(e)throw Error("Already flushing work.");
if(null!==f){var b=f;e=!0;try{var a=!0;do a=b(!0,g);while(a);a||(f=null);return!0}finally{e=!1}}else return!1}var n=[],r=[],K=1,h=null,d=3,B=!1,u=!1,y=!1,g=0,f=null,q=null,t=-1,l=null,w=-1,x=!1,e=!1,C=!1,G=!1,H=!1;a.reset=function(){if(e)throw Error("Cannot reset while already flushing work.");g=0;q=f=null;t=-1;l=null;w=-1;C=e=x=!1};a.unstable_IdlePriority=5;a.unstable_ImmediatePriority=1;a.unstable_LowPriority=4;a.unstable_NormalPriority=3;a.unstable_Profiling=null;a.unstable_UserBlockingPriority=
2;a.unstable_advanceTime=function(b){"disabledLog"===console.log.name||H||(g+=b,null!==q&&t<=g&&(q(g),t=-1,q=null))};a.unstable_cancelCallback=function(b){b.callback=null};a.unstable_clearYields=function(){if(null===l)return[];var b=l;l=null;return b};a.unstable_continueExecution=function(){u||B||(u=!0,f=F)};a.unstable_flushAll=function(){if(null!==l)throw Error("Log is not empty. Assert on the log of yielded values before flushing additional work.");J();if(null!==l)throw Error("While flushing work, something yielded a value. Use an assertion helper to assert on the log of yielded values, e.g. expect(Scheduler).toFlushAndYield([...])");
};a.unstable_flushAllWithoutAsserting=J;a.unstable_flushExpired=function(){if(e)throw Error("Already flushing work.");if(null!==f){e=!0;try{f(!1,g)||(f=null)}finally{e=!1}}};a.unstable_flushNumberOfYields=function(b){if(e)throw Error("Already flushing work.");if(null!==f){var a=f;w=b;e=!0;try{b=!0;do b=a(!0,g);while(b&&!x);b||(f=null)}finally{w=-1,e=x=!1}}};a.unstable_flushUntilNextPaint=function(){if(e)throw Error("Already flushing work.");if(null!==f){var a=f;G=!0;C=!1;e=!0;try{var k=!0;do k=a(!0,
g);while(k&&!x);k||(f=null)}finally{e=x=G=!1}}};a.unstable_forceFrameRate=function(){};a.unstable_getCurrentPriorityLevel=function(){return d};a.unstable_getFirstCallbackNode=function(){return m(n)};a.unstable_next=function(a){switch(d){case 1:case 2:case 3:var b=3;break;default:b=d}var c=d;d=b;try{return a()}finally{d=c}};a.unstable_now=function(){return g};a.unstable_pauseExecution=function(){};a.unstable_requestPaint=function(){C=!0};a.unstable_runWithPriority=function(a,e){switch(a){case 1:case 2:case 3:case 4:case 5:break;
default:a=3}var b=d;d=a;try{return e()}finally{d=b}};a.unstable_scheduleCallback=function(a,e,c){var b=g;"object"===typeof c&&null!==c?(c=c.delay,c="number"===typeof c&&0<c?b+c:b):c=b;switch(a){case 1:var d=-1;break;case 2:d=250;break;case 5:d=1073741823;break;case 4:d=1E4;break;default:d=5E3}d=c+d;a={id:K++,callback:e,priorityLevel:a,startTime:c,expirationTime:d,sortIndex:-1};c>b?(a.sortIndex=c,p(r,a),null===m(n)&&a===m(r)&&(y?(q=null,t=-1):y=!0,q=E,t=g+(c-b))):(a.sortIndex=d,p(n,a),u||B||(u=!0,
f=F));return a};a.unstable_setDisableYieldValue=function(a){H=a};a.unstable_shouldYield=I;a.unstable_wrapCallback=function(a){var b=d;return function(){var c=d;d=b;try{return a.apply(this,arguments)}finally{d=c}}};a.unstable_yieldValue=function(a){"disabledLog"===console.log.name||H||(null===l?l=[a]:l.push(a))}});
})();
