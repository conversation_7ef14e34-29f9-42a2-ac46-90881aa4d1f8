# DrawDB Angular Integration

Este projeto demonstra a integração do DrawDB (editor de diagramas ER) em uma aplicação Angular usando Module Federation como arquitetura de micro-frontends.

## 🏗️ Arquitetura

O projeto utiliza **Module Federation** para integrar uma aplicação React (DrawDB) como micro-frontend em uma aplicação Angular host:

```
┌─────────────────────────────────────┐
│           Angular Host              │
│         (Port 4200)                 │
│  ┌─────────────────────────────┐    │
│  │    DrawDB Wrapper           │    │
│  │    (Angular Component)      │    │
│  │                             │    │
│  │  ┌─────────────────────┐    │    │
│  │  │   React Remote      │    │    │
│  │  │   DrawDB Editor     │    │    │
│  │  │   (Port 4201)       │    │    │
│  │  └─────────────────────┘    │    │
│  └─────────────────────────────┘    │
└─────────────────────────────────────┘
```

## 📁 Estrutura do Projeto

```
drawdb_angular/
├── drawdb-remote/              # Aplicação React (Remote)
│   ├── src/
│   │   ├── DrawDBEditor.jsx    # Componente principal do DrawDB
│   │   ├── DrawDBEditor.css    # Estilos do editor
│   │   └── main.jsx           # Entry point
│   ├── vite.config.js         # Configuração Vite + Module Federation
│   └── package.json
│
├── drawdb-angular-host/        # Aplicação Angular (Host)
│   ├── src/app/
│   │   ├── drawdb-wrapper/     # Wrapper Angular para React
│   │   │   ├── drawdb-wrapper.ts
│   │   │   ├── drawdb-wrapper.html
│   │   │   └── drawdb-wrapper.scss
│   │   ├── services/
│   │   │   └── drawdb.ts       # Serviço Angular para estado
│   │   ├── app.ts              # Componente principal
│   │   └── app.html            # Template principal
│   ├── webpack.config.js       # Configuração Module Federation
│   └── package.json
│
├── angular-drawdb-simple/      # Projeto Angular de teste
├── README.md                   # Este arquivo
└── CHECKLIST.md               # Lista de progresso
```

## 🚀 Como Executar

### Pré-requisitos
- Node.js 18+
- npm ou yarn
- Angular CLI

### 1. Instalar Dependências

```bash
# React Remote
cd drawdb-remote
npm install

# Angular Host
cd ../drawdb-angular-host
npm install
```

### 2. Executar os Servidores

**Terminal 1 - React Remote:**
```bash
cd drawdb-remote
npm run dev
# Servidor rodará na porta 4201
```

**Terminal 2 - Angular Host:**
```bash
cd drawdb-angular-host
ng serve
# Servidor rodará na porta 4200
```

### 3. Acessar a Aplicação

Abra o navegador em: `http://localhost:4200`

## 🛠️ Tecnologias Utilizadas

### Frontend
- **Angular 20** - Framework principal
- **React 18** - Para o componente DrawDB
- **TypeScript** - Linguagem principal
- **SCSS** - Estilos

### Build & Bundling
- **Webpack 5** - Module Federation (Angular)
- **Vite** - Build tool (React)
- **@angular-architects/module-federation** - Plugin Angular
- **@originjs/vite-plugin-federation** - Plugin Vite

### UI Components
- **Semi-UI** - Biblioteca de componentes React
- **RxJS** - Gerenciamento de estado reativo (Angular)

## 🔧 Configuração Module Federation

### React Remote (drawdb-remote)
```javascript
// vite.config.js
federation({
  name: 'drawdb',
  filename: 'remoteEntry.js',
  exposes: {
    './DrawDBEditor': './src/DrawDBEditor.jsx',
  },
  shared: ['react', 'react-dom']
})
```

### Angular Host (drawdb-angular-host)
```javascript
// webpack.config.js
module.exports = withModuleFederationPlugin({
  name: 'drawdb-angular-host',
  remotes: {
    'drawdb': 'http://localhost:4201/remoteEntry.js',
  },
  shared: {
    ...shareAll({ singleton: true, strictVersion: true, requiredVersion: 'auto' }),
  },
});
```

## 📋 Funcionalidades Implementadas

### DrawDB Editor (React)
- ✅ Interface básica de edição
- ✅ Criação de tabelas
- ✅ Adição de campos
- ✅ Relacionamentos básicos
- ✅ Exportação SQL
- ✅ Temas (claro/escuro)

### Angular Integration
- ✅ Wrapper component para React
- ✅ Serviço de gerenciamento de estado
- ✅ Interfaces TypeScript
- ✅ Comunicação bidirecional
- ✅ Tratamento de erros

## 🔄 Estado do Projeto

**Status:** 🟡 Em desenvolvimento (85% completo)

### ✅ Concluído
- Arquitetura Module Federation
- Componentes React e Angular
- Configurações básicas
- Interfaces e tipos

### 🔄 Em Progresso
- Resolução de conflitos de configuração
- Testes de integração

### 📋 Próximos Passos
- Corrigir problemas de build
- Implementar funcionalidades avançadas
- Testes automatizados
- Documentação detalhada

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🔗 Links Úteis

- [DrawDB Original](https://github.com/drawdb-io/drawdb)
- [Module Federation](https://webpack.js.org/concepts/module-federation/)
- [Angular](https://angular.dev/)
- [React](https://react.dev/)
- [Semi-UI](https://semi.design/)

## 📞 Suporte

Para dúvidas ou problemas, abra uma issue no repositório.
