var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var IllustrationConstruction_exports = {};
__export(IllustrationConstruction_exports, {
  default: () => IllustrationConstruction_default
});
module.exports = __toCommonJS(IllustrationConstruction_exports);
var React = __toESM(require("react"));
function SvgComponent(props) {
  return /* @__PURE__ */ React.createElement("svg", __spreadValues({
    width: 200,
    height: 200,
    viewBox: "0 0 200 200",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    focusable: false,
    "aria-hidden": true
  }, props), /* @__PURE__ */ React.createElement("rect", {
    width: 200,
    height: 200,
    fill: "transparent"
  }), /* @__PURE__ */ React.createElement("ellipse", {
    cx: 175.426,
    cy: 90.4577,
    rx: 16.5618,
    ry: 67.0109,
    fill: "var(--semi-color-primary-light-default)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M179.01 25.02c-7.42 6.63-12.97 33.4-12.97 65.44 0 32.02 5.55 58.8 12.97 65.43a5.4 5.4 0 0 1-3.58 1.58c-9.15 0-16.57-30-16.57-67.01 0-37.01 7.42-67.02 16.57-67.02a5.4 5.4 0 0 1 3.58 1.58Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M12.66 36.64c1.67-5.91 7.26-9.61 12.7-12.15 1.85 2.97 4.3 5.94 7.09 7 3.16 1.2 6.1-1.62 6.47-4.29.2-1.31-1.1-4.15-2.04-5.28-.93-1.12-2.4-1.92-4.43-2.18a14.4 14.4 0 0 0-7.09 1.1c-1.62-3-2.54-6.6-2.37-10.14l-1.38-.1c-.18 3.82.78 7.63 2.44 10.82-5.63 2.72-10.9 8.34-12.7 14.71l1.31.5Z",
    fill: "var(--semi-color-primary-light-hover)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M20.7 23.93c-3.71 2.46-6.75 5.94-7.98 9.64l-1.3-.43c1.34-4.08 4.63-7.78 8.53-10.36a24.77 24.77 0 0 1 4.16-2.22 16.19 16.19 0 0 1-2.44-9.23l1.38.08c-.17 3.03.75 6.1 2.37 8.66a16.61 16.61 0 0 1 7.09-.94c2.03.22 3.5.9 4.43 1.87a3.8 3.8 0 0 1 1.08 3.28c-.38 2.29-2.83 3.92-5.99 2.9a14.7 14.7 0 0 1-7.14-5.45 23.28 23.28 0 0 0-4.18 2.2Zm5.55-2.68a13.2 13.2 0 0 0 6.2 4.62c2.5.8 4-.5 4.21-1.81a2.42 2.42 0 0 0-.71-2.1c-.65-.67-1.78-1.26-3.6-1.46-1.9-.2-4 .08-6.1.75Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M174.3 53.76a11.24 11.24 0 0 1-2.75 15.66 11.24 11.24 0 0 1-15.7-2.53 11.24 11.24 0 0 1 2.76-15.66 11.24 11.24 0 0 1 15.7 2.53Z",
    fill: "white",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M154.18 64.88c3.35-.11 6.93-1.44 9.38-5.07l1.32-1.96c2.35-3.54 3.1-4.65 4.75-4.65.5 0 1.07.1 1.7.22 1.08.21 2.3.44 3.46.16l-.08-.11a11.74 11.74 0 0 0-16.39-2.65 11.8 11.8 0 0 0-4.14 14.06Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("ellipse", {
    cx: 174.584,
    cy: 58.9818,
    rx: 0.826454,
    ry: 0.964193,
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M166.48 57.62a2.37 2.37 0 1 1-3.87 2.75 2.37 2.37 0 0 1 3.87-2.75Z",
    fill: "white",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M66.29 27.92c2.24 0 3.99-1.76 3.99-3.86 0-2.1-1.75-3.86-4-3.86-2.23 0-3.99 1.76-3.99 3.86 0 2.1 1.76 3.86 4 3.86Zm0 1.38a5.3 5.3 0 0 0 5.37-5.24 5.3 5.3 0 0 0-5.37-5.23 5.3 5.3 0 0 0-5.38 5.23 5.3 5.3 0 0 0 5.38 5.24Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M128.27 45.4c2.24 0 4-1.76 4-3.85 0-2.1-1.76-3.86-4-3.86s-4 1.76-4 3.86c0 2.1 1.76 3.85 4 3.85Zm0 1.38a5.3 5.3 0 0 0 5.37-5.23 5.3 5.3 0 0 0-5.37-5.24 5.3 5.3 0 0 0-5.37 5.24 5.3 5.3 0 0 0 5.37 5.23Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M64.6 65.95 55 48.38l23.93 7.12L64.6 65.95Z",
    fill: "var(--semi-color-primary-light-hover)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "m58.08 48.38 18.16 5.52-10.42 7.27-.79-1.13 8.1-5.65-15.45-4.7.4-1.31Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M103.65 37.15c-1.41 1.57-6.55 7.1-7.82 8.37 3.01-3.17 1.42-6.8-.74-7.44l-.61-.17c-2.41-.69-6.42-1.82-4.43-4.9 2.17-3.35.83-5.95-3.17-7.4 1.43-1.74 7.76-8.85 8.33-8.63h.02l.22-.6c2.08.75 3.62 1.85 4.3 3.35.69 1.53.38 3.27-.78 5.07-.47.72-.48 1.2-.37 **********.44.71 1.01 ********** 2.26.95 3.36 1.27l.65.18c2.87.86 3.84 4.78.03 8.32Zm-8.2 8.74Z",
    fill: "var(--semi-color-primary-light-hover)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M103.29 36.76c3.2-2.97 1.54-6.15-.1-6.64l-.56-.16c-1.1-.31-2.64-.75-3.77-1.44-.69-.42-1.33-1-1.6-1.8-.28-.84-.1-1.75.52-2.7 1-1.56 1.11-2.8.68-3.76-.46-1-1.59-1.93-3.5-2.62l.46-1.3c2.07.75 3.62 1.86 4.3 3.35.69 1.54.38 3.28-.79 5.07-.46.72-.48 1.2-.37 **********.45.72 1.02 ********** 2.26.96 3.36 1.27l.65.19c2.87.85 4.45 5.43.64 8.97l-.94-1Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M40.57 60.4c-.05 1.6-1.2 8.02-1.32 9.38.2-3.33-2.5-4.66-4.08-3.97l-.44.2c-1.74.78-4.64 2.09-4.98-.68-.39-3.02-2.44-3.87-5.48-2.75-.03-1.72 1.04-9.64 1.49-9.8h.01l-.17-.46c1.58-.58 3.02-.7 4.14-.16 1.16.56 1.84 1.72 2.04 **********.31.93.53 **********.61.2 **********-.13 1.8-.56 2.59-.91l.47-.21c2.08-.91 4.58.89 4.1 4.83Zm-1.35 9.78Z",
    fill: "var(--semi-color-primary-light-hover)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M40.17 60.35c.41-3.3-2.12-4.34-3.31-3.83l-.41.19c-.8.35-1.9.86-2.9 1.01-.61.1-1.27.07-1.82-.26-.58-.35-.92-.97-1.03-1.83-.17-1.4-.72-2.18-1.44-2.52-.76-.36-1.87-.35-3.33.2l-.37-1c1.58-.57 3.02-.69 4.15-.15 1.16.55 1.83 1.72 2.03 **********.31.93.53 **********.62.2 **********-.12 1.78-.55 2.58-.9l.47-.22c2.09-.9 5.26.98 4.78 4.92l-1.05-.13Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M165.83 33.67c-.64 3.57 1.4 5.27 5.24 5.08-1.67 1.04-8.8 5.26-10.02 5.42-1.52.2-5.3-.94-5.06-3.83.25-2.9-.05-4.17-2.69-4.03-2.63.15-3.75 1.24-5.52-.95-1.76-2.2-2.15-2.79-.9-5.18 1-1.91 8.19-5.09 9.9-5.39-1.78 3.96.94 6.64 3.05 6.4 2.12-.22 6.64-1.08 6 2.48Z",
    fill: "var(--semi-color-primary-light-hover)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M147.34 30.58c-1.62 3.58.86 5.6 2.34 5.44l.53-.07a14.4 14.4 0 0 1 3.68-.11c.72.1 1.48.36 2 .*********** 1.47.54 2.5-.3 1.64.04 2.69.72 ********** 1.97 1.04 3.81.94l.07 1.38c-2 .1-3.7-.28-4.81-1.31-1.14-1.06-1.5-2.65-1.15-4.57.14-.76-.02-1.14-.2-1.35-.22-.24-.6-.42-1.19-.51-1-.16-2.2 0-3.24.12l-.61.07c-2.76.3-5.72-3.04-3.75-7.38l1.26.57Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M32.4 99.7c.17 2.63-.97 4.05-3.42 3.66-2.46-.4-1.81 2.69-1.63 4.14.18 1.45-1.62 3.34-4.36 2.16l.47-10.92c1.34.5 1.4.8 3.54-.49 2.13-1.29.75-4.83 1.24-5.62.5-.8 1.05.02 3.09-.35.77-.15 1.18-.72 1.38-1.39l.07-2.17c.1.55.15 1.43-.07 2.17l-.31 8.8Z",
    fill: "var(--semi-color-primary-light-hover)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M23.65 98.01c2.35 1.02 3.5-.63 3.4-1.45l-.05-.35c-.1-.7-.24-1.73-.13-2.59.07-.52.25-1.1.7-1.52.5-.44 1.15-.56 1.9-.44 1.08.17 1.71-.07 2.07-.47.4-.44.63-1.23.55-2.47l1.37-.09c.1 1.4-.14 2.64-.9 3.48-.77.86-1.94 1.13-3.31.9-.48-.07-.66.04-.74.11-.11.1-.22.3-.27.69-.1.65.02 1.44.12 2.15l.05.43c.27 2.1-2.2 4.24-5.31 2.89l.55-1.27Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M149.6 62.18c3.54 3.54 6.73 8.55 6.87 14.08 0 3.1-.15 7.4-1.6 13.37-.66 6.73-2.03 20.43-2.28 21.37l12.18.2c.34-1.17 2.82-11.62 3.83-14.11a9.5 9.5 0 0 1 1.61-2.85c.63-.71 1.4-.99 2.12-1.32.8-.34 1.74-.46 2.9-.34l.56.08c1.03.18 2.04.54 2.9 1.13a5.9 5.9 0 0 1 2.5 4.5c.66 6.6.2 13.73-.28 20.37v.03c-.14 1.96-.84 11.64-4.67 14.9-2.24 1.88-14.75 2.34-26.43 2.36l-2.46 22.88c4.48-.23 12.72 1.43 15.14 8.55 2.41 7.12-4.21 7.6-11.06 7.88-5.48.21-19.5.09-25.55 0-5.37 4.62-13.17 7.44-24.07 7.13-19.55-.56-60.95-4.4-61.9-4.1 1.08.82 3.23 2.75 3.2 4.02-.03 1.58-2.4 2.8-11.17 2.9-8.76.1-14.34-2.52-14.16-7.01.04-.97.32-2.39.78-4.1-5.28-.66-11.49-.52-10.43-8.1 1.72-12.31 15.88-29.62 19.83-32.02 3.95-2.4 11.5-1.8 16.77-1.11-3.28-5.06-5.6-11.87-4.33-20.6.65-4.43 2.75-9.81 7.02-15.42a82.24 82.24 0 0 1 3.67-4.45c10.28-11.67 25.82-23.79 35.5-31.07 5.89-4.43 14.79-7.84 19.87-9.17 18.61-4.85 33.02-.96 43.14 10.02Z",
    fill: "white"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M146.52 110.94c-3.71 6.73-4.9 7.34-6.3 7.13-1.4-.2-2.14-4.66.84-6.29 1.09-.6 2.94-.84 5.46-.84Zm0 0c4.52-8.8 6.99-15.77 8.34-21.31m-8.34 21.31c1.56 0 3.75.03 6.07.06m-22.63 24.68c-7.87-.16-21.74.4-27.73-9.35a16.2 16.2 0 0 1-2.27-6.16m30 15.51c3.6 4.6 7.74 14.23 4.31 26.15-1.52 5.27-4.13 9.76-8.39 13.43m4.08-39.58c3.32.07 11.36.29 19.85.27m-40.95-46.5c-5.27 13.02-10.13 22.49-8.9 30.72m0 0c-1.3-.6-11.71-.98-17.74 1.34-4.9-1.34-31.24-13.22-31.13-29.1M39.92 178.3c.94-.32 42.34 3.53 61.89 4.09 10.9.31 18.7-2.51 24.07-7.13m-85.96 3.04a84.2 84.2 0 0 1-6.88-1.57m6.88 1.57c1.07.8 3.22 2.74 3.19 4-.03 1.59-2.4 2.81-11.17 2.91-8.76.1-14.34-2.52-14.16-7.01.04-.97.32-2.39.78-4.1m34.41-32.49c-2.46-.06-9.86-.78-14.88-.27-1.3.14-5.48 1.25-10.13 9.6a120.44 120.44 0 0 0-9.4 23.15m34.41-32.48c2.46.07 22.49 1.59 25.99 4.41 3.5 2.83-2.93 3.67-7.85 2.9-4.91-.78-11.47-1.72-18.14-7.3Zm0 0a35.32 35.32 0 0 1-8.24-8.74M51.1 92.4a82.24 82.24 0 0 0-3.67 4.45c-4.27 5.6-6.37 11-7.02 15.42-1.28 8.73 1.05 15.54 4.33 20.6M51.1 92.4c10.28-11.67 25.82-23.79 35.5-31.07 5.89-4.43 14.79-7.84 19.87-9.17 18.61-4.85 33.02-.96 43.14 10.02 3.54 3.54 6.73 8.55 6.87 14.08 0 3.1-.15 7.4-1.6 13.37M44.72 132.87c-5.27-.7-12.82-1.3-16.77 1.1C24.01 136.39 9.85 153.7 8.13 166c-1.06 7.59 5.15 7.45 10.43 8.1m136.3-84.46c-.65 6.73-2.02 20.43-2.27 21.37m0 0 12.18.2c.34-1.17 2.82-11.62 3.83-14.11a9.5 9.5 0 0 1 1.61-2.85c.63-.71 1.4-.99 2.12-1.32m-22.52 43.03c11.68-.02 24.2-.48 26.43-2.37 3.85-3.26 4.53-13.02 4.67-14.92.47-6.64.94-13.76.28-20.38a5.9 5.9 0 0 0-2.5-4.49m-28.88 42.16-2.46 22.88c4.48-.23 12.72 1.43 15.14 8.55 2.41 7.12-4.21 7.6-11.06 7.88-5.48.21-19.5.09-25.55 0m23.35-9.25c2.2.1 5.64.7 7.84 3.88 1.17 1.7.52 3.27-.62 3.94-1 .58-2.38.38-2.96.14-1.44-.57-4.87-1.1-6.22-.87-1.34.22-3.19 1.58-4.98 1.58-1.79 0-3.83-.67-4.9-2.1m41.3-78.79c-1.28.56-2.06 2.7-2.34 4.32-.27 1.63-1.23 7.95-1.32 9.48m3.66-13.8a7.28 7.28 0 0 0-2.9-1.13m0 0a9.2 9.2 0 0 0-.57-.08 5.73 5.73 0 0 0-2.9.34m3.47-.26c-1.15.4-2.14 2.47-2.57 3.82-.42 1.36-.96 3.14-2.22 10.24m1.33-13.8c-1.7 1.97-3.2 6.4-4.5 13.1",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M44.53 141.29c-.7-.74-2.59-7-2.67-8.77l2.67.23c1.9 3.58 6.4 7.37 8.43 8.82-2.2-.1-6.95-.28-8.43-.28Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M135.3 175.44c-2.57-1.04-3.2-4.76-3-8.7-.89 2.79-4.1 6.89-6.49 8.7h9.5Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M121.71 140.8c-8.99-2.04-14.92-6.8-17.57-11.67.46.39 3.28 2.57 5.93 3.8 6.86 3.17 18.16 2.8 20.05 2.8 1.23 1.47 2.88 4.85 3.52 6.3-1.1 0-6.21.06-11.93-1.23Z",
    fill: "#515151"
  }));
}
var IllustrationConstruction_default = SvgComponent;
